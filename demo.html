<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MV Studio - Experiências Audiovisuais Memoráveis</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Glass morphism utilities */
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .glass-dark {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Animations */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        @keyframes orb {
            0%, 100% { transform: translate(0px, 0px) scale(1); }
            33% { transform: translate(30px, -30px) scale(1.1); }
            66% { transform: translate(-20px, 20px) scale(0.9); }
        }
        
        .animate-float {
            animation: float 6s ease-in-out infinite;
        }
        
        .animate-orb {
            animation: orb 8s ease-in-out infinite;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }
        
        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }
    </style>
</head>
<body class="bg-black text-white font-sans">
    <!-- Navbar -->
    <nav class="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 w-[90%] max-w-5xl">
        <div class="glass rounded-xl px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="text-2xl font-bold text-white">MV Studio</div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#inicio" class="text-white/80 hover:text-white transition-colors">Início</a>
                    <a href="#projetos" class="text-white/80 hover:text-white transition-colors">Projetos</a>
                    <a href="#albuns" class="text-white/80 hover:text-white transition-colors">Álbuns</a>
                    <a href="#sobre" class="text-white/80 hover:text-white transition-colors">Sobre</a>
                    <a href="#catalogos" class="text-white/80 hover:text-white transition-colors">Catálogos</a>
                    <a href="#blog" class="text-white/80 hover:text-white transition-colors">No Meu Mundo</a>
                </div>
                <button class="bg-white text-black px-6 py-2 rounded-lg hover:bg-white/90 transition-colors">
                    Vamos Falar
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="inicio" class="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-black via-gray-900 to-black">
        <!-- Animated Background Orbs -->
        <div class="absolute inset-0">
            <div class="absolute top-20 left-20 w-48 h-48 bg-gradient-to-br from-blue-400/30 to-cyan-400/30 rounded-full blur-sm animate-orb"></div>
            <div class="absolute top-40 right-32 w-32 h-32 bg-gradient-to-br from-purple-400/30 to-pink-400/30 rounded-full blur-sm animate-orb" style="animation-delay: 2s;"></div>
            <div class="absolute bottom-32 left-40 w-24 h-24 bg-gradient-to-br from-pink-400/30 to-rose-400/30 rounded-full blur-sm animate-orb" style="animation-delay: 4s;"></div>
            <div class="absolute bottom-20 right-20 w-32 h-32 bg-gradient-to-br from-white/20 to-white/10 rounded-full blur-sm animate-orb" style="animation-delay: 6s;"></div>
        </div>

        <!-- Floating Video Elements -->
        <div class="absolute inset-0 pointer-events-none">
            <div class="absolute top-32 right-20 w-32 h-20 glass rounded-lg animate-float flex items-center justify-center">
                <svg class="w-6 h-6 text-white/60" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                </svg>
            </div>
            <div class="absolute bottom-40 left-20 w-40 h-24 glass rounded-lg animate-float flex items-center justify-center" style="animation-delay: 2s;">
                <svg class="w-7 h-7 text-white/60" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                </svg>
            </div>
        </div>

        <!-- Main Content -->
        <div class="relative z-10 text-center px-4 max-w-4xl mx-auto">
            <div class="glass rounded-xl p-8 md:p-12">
                <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
                    MV Studio
                </h1>
                <p class="text-xl md:text-2xl text-white/80 mb-8 leading-relaxed">
                    Os melhores editores, filmmakers e fotógrafos num só lugar
                </p>
                <p class="text-lg md:text-xl text-white/60 mb-10 max-w-2xl mx-auto">
                    Transformamos ideias em experiências audiovisuais memoráveis
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                    <button class="bg-white text-black px-8 py-4 rounded-lg hover:bg-white/90 transition-colors min-w-[200px] text-lg font-medium">
                        Explorar Portfólio
                    </button>
                    <button class="glass px-8 py-4 rounded-lg hover:bg-white/20 transition-colors min-w-[200px] text-lg font-medium">
                        Vamos Falar
                    </button>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <svg class="w-8 h-8 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
            </svg>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projetos" class="py-20 px-4 bg-gradient-to-b from-black to-gray-900">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
                    Nossos Projetos
                </h2>
                <p class="text-xl text-white/70 max-w-2xl mx-auto">
                    Uma seleção dos nossos trabalhos mais impactantes em vídeo, fotografia e produção audiovisual
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="glass rounded-xl overflow-hidden group hover:bg-white/20 transition-all duration-300">
                    <div class="h-64 bg-gradient-to-br from-blue-500/20 to-purple-500/20 relative">
                        <div class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            <button class="glass px-6 py-3 rounded-lg">Ver Projeto</button>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-2">Campanha Publicitária</h3>
                        <p class="text-white/60 text-sm mb-4">Produção audiovisual completa para lançamento de produto premium</p>
                        <div class="flex gap-2">
                            <span class="text-xs bg-white/10 text-white/80 px-2 py-1 rounded-full">Vídeo</span>
                            <span class="text-xs bg-white/10 text-white/80 px-2 py-1 rounded-full">Fotografia</span>
                        </div>
                    </div>
                </div>

                <div class="glass rounded-xl overflow-hidden group hover:bg-white/20 transition-all duration-300">
                    <div class="h-64 bg-gradient-to-br from-purple-500/20 to-pink-500/20 relative">
                        <div class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            <button class="glass px-6 py-3 rounded-lg">Ver Projeto</button>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-2">Clipe Musical</h3>
                        <p class="text-white/60 text-sm mb-4">Direção e produção de videoclipe com narrativa visual única</p>
                        <div class="flex gap-2">
                            <span class="text-xs bg-white/10 text-white/80 px-2 py-1 rounded-full">Clipe</span>
                            <span class="text-xs bg-white/10 text-white/80 px-2 py-1 rounded-full">Direção</span>
                        </div>
                    </div>
                </div>

                <div class="glass rounded-xl overflow-hidden group hover:bg-white/20 transition-all duration-300">
                    <div class="h-64 bg-gradient-to-br from-green-500/20 to-emerald-500/20 relative">
                        <div class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            <button class="glass px-6 py-3 rounded-lg">Ver Projeto</button>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-2">Documentário Corporativo</h3>
                        <p class="text-white/60 text-sm mb-4">Storytelling empresarial com foco em valores e cultura organizacional</p>
                        <div class="flex gap-2">
                            <span class="text-xs bg-white/10 text-white/80 px-2 py-1 rounded-full">Documentário</span>
                            <span class="text-xs bg-white/10 text-white/80 px-2 py-1 rounded-full">Corporativo</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="sobre" class="py-20 px-4 bg-gradient-to-b from-gray-900 to-black">
        <div class="max-w-6xl mx-auto">
            <div class="text-center mb-20">
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-8">
                    Sobre a MV Studio
                </h2>
                <div class="max-w-4xl mx-auto">
                    <p class="text-xl md:text-2xl text-white/80 leading-relaxed mb-8">
                        Somos mais que um estúdio audiovisual. Somos contadores de histórias, 
                        criadores de memórias e arquitetos de experiências visuais que tocam o coração.
                    </p>
                    <p class="text-lg text-white/70 leading-relaxed">
                        Na MV Studio, reunimos os melhores editores, filmmakers e fotógrafos num só lugar, 
                        unidos pela paixão de transformar ideias em experiências audiovisuais memoráveis.
                    </p>
                </div>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-20">
                <div class="glass text-center p-6 rounded-xl hover:bg-white/20 transition-colors">
                    <h3 class="text-2xl md:text-3xl font-bold text-white mb-2">500+</h3>
                    <p class="text-white/70 text-sm">Projetos Realizados</p>
                </div>
                <div class="glass text-center p-6 rounded-xl hover:bg-white/20 transition-colors">
                    <h3 class="text-2xl md:text-3xl font-bold text-white mb-2">200+</h3>
                    <p class="text-white/70 text-sm">Clientes Satisfeitos</p>
                </div>
                <div class="glass text-center p-6 rounded-xl hover:bg-white/20 transition-colors">
                    <h3 class="text-2xl md:text-3xl font-bold text-white mb-2">15+</h3>
                    <p class="text-white/70 text-sm">Prêmios Conquistados</p>
                </div>
                <div class="glass text-center p-6 rounded-xl hover:bg-white/20 transition-colors">
                    <h3 class="text-2xl md:text-3xl font-bold text-white mb-2">1000+</h3>
                    <p class="text-white/70 text-sm">Horas de Conteúdo</p>
                </div>
            </div>

            <div class="glass-dark rounded-xl p-8 md:p-12 text-center">
                <h3 class="text-3xl md:text-4xl font-bold text-white mb-8">Nossa Missão</h3>
                <p class="text-xl md:text-2xl text-white/80 leading-relaxed max-w-4xl mx-auto">
                    "Transformar momentos em eternidade, ideias em realidade visual, 
                    e sonhos em experiências que transcendem o tempo. Acreditamos que 
                    cada imagem tem o poder de contar uma história única e cada vídeo 
                    pode despertar emoções profundas."
                </p>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contato" class="py-20 px-4 bg-gradient-to-b from-black to-gray-900">
        <div class="max-w-4xl mx-auto text-center">
            <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
                Vamos Falar
            </h2>
            <p class="text-xl text-white/70 mb-12">
                Pronto para transformar sua ideia em realidade? Entre em contato conosco e vamos criar algo extraordinário juntos.
            </p>
            
            <div class="glass rounded-xl p-8">
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <input type="text" placeholder="Seu nome" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50">
                        <input type="email" placeholder="<EMAIL>" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50">
                    </div>
                    <select class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-white/50">
                        <option value="" class="bg-gray-800">Selecione um serviço</option>
                        <option value="fotografia" class="bg-gray-800">Fotografia de Eventos</option>
                        <option value="video" class="bg-gray-800">Produção de Vídeo</option>
                        <option value="corporativo" class="bg-gray-800">Fotografia Corporativa</option>
                    </select>
                    <textarea rows="5" placeholder="Conte-nos sobre seu projeto..." class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none"></textarea>
                    <button type="submit" class="w-full bg-white text-black px-8 py-4 rounded-lg hover:bg-white/90 transition-colors text-lg font-medium">
                        Enviar Mensagem
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gradient-to-t from-black to-gray-900 pt-20 pb-8">
        <div class="max-w-7xl mx-auto px-4">
            <div class="glass-dark rounded-xl p-8 md:p-12 mb-8">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <div class="lg:col-span-2">
                        <h3 class="text-3xl font-bold text-white mb-4">MV Studio</h3>
                        <p class="text-white/70 leading-relaxed mb-6 max-w-md">
                            Transformamos ideias em experiências audiovisuais memoráveis. 
                            Os melhores editores, filmmakers e fotógrafos num só lugar.
                        </p>
                        <div class="space-y-3 text-white/60 text-sm">
                            <div class="flex items-center space-x-2">
                                <span>📍</span>
                                <span>São Paulo, SP - Brasil</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span>📞</span>
                                <span>+55 (11) 99999-9999</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span>✉️</span>
                                <span><EMAIL></span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-white mb-4">Navegação</h4>
                        <ul class="space-y-2">
                            <li><a href="#inicio" class="text-white/60 hover:text-white transition-colors text-sm">Início</a></li>
                            <li><a href="#projetos" class="text-white/60 hover:text-white transition-colors text-sm">Projetos</a></li>
                            <li><a href="#albuns" class="text-white/60 hover:text-white transition-colors text-sm">Álbuns</a></li>
                            <li><a href="#sobre" class="text-white/60 hover:text-white transition-colors text-sm">Sobre</a></li>
                            <li><a href="#catalogos" class="text-white/60 hover:text-white transition-colors text-sm">Catálogos</a></li>
                            <li><a href="#blog" class="text-white/60 hover:text-white transition-colors text-sm">No Meu Mundo</a></li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-white mb-4">Serviços</h4>
                        <ul class="space-y-2">
                            <li><span class="text-white/60 text-sm">Fotografia de Eventos</span></li>
                            <li><span class="text-white/60 text-sm">Produção de Vídeo</span></li>
                            <li><span class="text-white/60 text-sm">Fotografia Corporativa</span></li>
                            <li><span class="text-white/60 text-sm">Clipes Musicais</span></li>
                            <li><span class="text-white/60 text-sm">Documentários</span></li>
                            <li><span class="text-white/60 text-sm">Reels para Redes Sociais</span></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-white/10 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center text-white/60 text-sm">
                    <p class="mb-4 md:mb-0">© 2024 MV Studio. Feito com ❤️ para criar experiências únicas.</p>
                    <div class="flex space-x-6">
                        <a href="#" class="hover:text-white transition-colors">Política de Privacidade</a>
                        <a href="#" class="hover:text-white transition-colors">Termos de Uso</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scroll to Top Button -->
    <button onclick="window.scrollTo({top: 0, behavior: 'smooth'})" class="fixed bottom-8 right-8 glass rounded-full p-3 text-white hover:bg-white/20 transition-all duration-300 z-40">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"/>
        </svg>
    </button>
</body>
</html>
