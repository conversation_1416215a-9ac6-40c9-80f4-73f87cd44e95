<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MV Studio - Experiências Audiovisuais Memoráveis</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Glass morphism utilities */
        .glass {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .glass-dark {
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Animations */
        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg) scale(1);
            }
            25% {
                transform: translateY(-15px) rotate(2deg) scale(1.02);
            }
            50% {
                transform: translateY(-25px) rotate(-1deg) scale(0.98);
            }
            75% {
                transform: translateY(-10px) rotate(3deg) scale(1.05);
            }
        }

        @keyframes orb {
            0%, 100% {
                transform: translate(0px, 0px) scale(1) rotate(0deg);
                opacity: 0.6;
            }
            33% {
                transform: translate(30px, -30px) scale(1.2) rotate(120deg);
                opacity: 0.8;
            }
            66% {
                transform: translate(-20px, 20px) scale(0.8) rotate(240deg);
                opacity: 0.4;
            }
        }

        .animate-float {
            animation: float 8s ease-in-out infinite;
        }

        .animate-orb {
            animation: orb 12s ease-in-out infinite;
        }

        /* Cinematic text effects */
        .text-shadow-cinematic {
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.8), 0 2px 4px rgba(0, 0, 0, 0.6);
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }
        
        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }
    </style>
</head>
<body class="bg-black text-white font-sans">
    <!-- Navbar -->
    <nav class="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 w-[95%] max-w-7xl">
        <div class="glass rounded-2xl px-8 py-4 backdrop-blur-xl">
            <div class="flex items-center justify-between">
                <!-- Logo -->
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-amber-400 to-orange-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-black" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17 10.5V7a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h12a1 1 0 001-1v-3.5l4 4v-11l-4 4z"/>
                        </svg>
                    </div>
                    <span class="text-xl font-bold text-white">MV Studio</span>
                </div>

                <!-- Navigation Links -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#inicio" class="text-amber-400 text-sm font-medium border-b border-amber-400 pb-1">Início</a>
                    <a href="#projetos" class="text-white/70 hover:text-white transition-colors text-sm font-medium">Projetos</a>
                    <a href="#albuns" class="text-white/70 hover:text-white transition-colors text-sm font-medium">Álbuns</a>
                    <a href="#sobre" class="text-white/70 hover:text-white transition-colors text-sm font-medium">Sobre</a>
                    <a href="#catalogos" class="text-white/70 hover:text-white transition-colors text-sm font-medium">Catálogos</a>
                    <a href="#blog" class="text-white/70 hover:text-white transition-colors text-sm font-medium">No meu mundo</a>
                </div>

                <!-- CTA Button -->
                <button class="bg-white text-black px-6 py-2.5 rounded-full hover:bg-gray-100 transition-colors text-sm font-semibold">
                    vamos falar
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="inicio" class="relative min-h-screen flex items-center overflow-hidden bg-black">
        <!-- Background Image/Video Placeholder -->
        <div class="absolute inset-0 bg-gradient-to-r from-black via-black/80 to-transparent z-10"></div>
        <div class="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/60 z-10"></div>

        <!-- Cinematic Background -->
        <div class="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-40" style="background-image: linear-gradient(45deg, #1a1a1a 25%, transparent 25%), linear-gradient(-45deg, #1a1a1a 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #1a1a1a 75%), linear-gradient(-45deg, transparent 75%, #1a1a1a 75%); background-size: 60px 60px; background-position: 0 0, 0 30px, 30px -30px, -30px 0px;"></div>

        <!-- 3D Floating Elements -->
        <div class="absolute inset-0 pointer-events-none">
            <!-- Film Strip Elements -->
            <div class="absolute top-20 right-10 w-16 h-80 bg-gradient-to-b from-gray-800/40 to-gray-900/40 rounded-lg transform rotate-12 animate-float" style="background-image: repeating-linear-gradient(0deg, transparent, transparent 20px, rgba(255,255,255,0.1) 20px, rgba(255,255,255,0.1) 25px);"></div>
            <div class="absolute bottom-20 left-10 w-12 h-60 bg-gradient-to-b from-gray-700/30 to-gray-800/30 rounded-lg transform -rotate-6 animate-float" style="animation-delay: 2s; background-image: repeating-linear-gradient(0deg, transparent, transparent 15px, rgba(255,255,255,0.08) 15px, rgba(255,255,255,0.08) 18px);"></div>

            <!-- Floating Camera/Video Elements -->
            <div class="absolute top-40 right-32 glass rounded-xl p-4 animate-float transform rotate-3" style="animation-delay: 1s;">
                <svg class="w-8 h-8 text-white/60" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17 10.5V7a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h12a1 1 0 001-1v-3.5l4 4v-11l-4 4z"/>
                </svg>
            </div>

            <div class="absolute bottom-32 right-20 glass rounded-full p-3 animate-float transform -rotate-6" style="animation-delay: 3s;">
                <svg class="w-6 h-6 text-white/50" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
            </div>

            <!-- Animated Orbs (mantendo elementos 3D) -->
            <div class="absolute top-32 left-20 w-32 h-32 bg-gradient-to-br from-amber-400/20 to-orange-400/20 rounded-full blur-lg animate-orb"></div>
            <div class="absolute bottom-40 right-40 w-24 h-24 bg-gradient-to-br from-blue-400/15 to-cyan-400/15 rounded-full blur-lg animate-orb" style="animation-delay: 4s;"></div>
        </div>

        <!-- Main Content -->
        <div class="relative z-20 px-4 max-w-7xl mx-auto w-full">
            <div class="max-w-4xl">
                <h1 class="text-5xl md:text-7xl lg:text-8xl font-bold text-white mb-8 leading-tight tracking-tight text-shadow-cinematic">
                    Filmes que carregam<br>
                    <span class="text-transparent bg-clip-text bg-gradient-to-r from-white to-gray-300">emoções</span>, frames que<br>
                    <span class="text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-orange-400">guardam histórias</span>.
                </h1>

                <p class="text-xl md:text-2xl text-gray-300 mb-12 max-w-2xl leading-relaxed">
                    O vídeo que a sua marca precisa, nós criamos com paixão.
                </p>

                <div class="flex flex-col sm:flex-row gap-6 items-start">
                    <button class="bg-gradient-to-r from-amber-500 to-orange-500 text-black px-8 py-4 rounded-full hover:from-amber-400 hover:to-orange-400 transition-all duration-300 text-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-105">
                        Fale conosco
                    </button>

                    <div class="text-sm text-gray-400 uppercase tracking-wider mt-4 sm:mt-6">
                        AUDIOVISUAL
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce z-20">
            <svg class="w-6 h-6 text-white/40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
            </svg>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projetos" class="py-20 px-4 bg-gradient-to-b from-black to-gray-900">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
                    Nossos Projetos
                </h2>
                <p class="text-xl text-white/70 max-w-2xl mx-auto">
                    Uma seleção dos nossos trabalhos mais impactantes em vídeo, fotografia e produção audiovisual
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="glass rounded-xl overflow-hidden group hover:bg-white/20 transition-all duration-300">
                    <div class="h-64 bg-gradient-to-br from-blue-500/20 to-purple-500/20 relative">
                        <div class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            <button class="glass px-6 py-3 rounded-lg">Ver Projeto</button>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-2">Campanha Publicitária</h3>
                        <p class="text-white/60 text-sm mb-4">Produção audiovisual completa para lançamento de produto premium</p>
                        <div class="flex gap-2">
                            <span class="text-xs bg-white/10 text-white/80 px-2 py-1 rounded-full">Vídeo</span>
                            <span class="text-xs bg-white/10 text-white/80 px-2 py-1 rounded-full">Fotografia</span>
                        </div>
                    </div>
                </div>

                <div class="glass rounded-xl overflow-hidden group hover:bg-white/20 transition-all duration-300">
                    <div class="h-64 bg-gradient-to-br from-purple-500/20 to-pink-500/20 relative">
                        <div class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            <button class="glass px-6 py-3 rounded-lg">Ver Projeto</button>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-2">Clipe Musical</h3>
                        <p class="text-white/60 text-sm mb-4">Direção e produção de videoclipe com narrativa visual única</p>
                        <div class="flex gap-2">
                            <span class="text-xs bg-white/10 text-white/80 px-2 py-1 rounded-full">Clipe</span>
                            <span class="text-xs bg-white/10 text-white/80 px-2 py-1 rounded-full">Direção</span>
                        </div>
                    </div>
                </div>

                <div class="glass rounded-xl overflow-hidden group hover:bg-white/20 transition-all duration-300">
                    <div class="h-64 bg-gradient-to-br from-green-500/20 to-emerald-500/20 relative">
                        <div class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                            <button class="glass px-6 py-3 rounded-lg">Ver Projeto</button>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-2">Documentário Corporativo</h3>
                        <p class="text-white/60 text-sm mb-4">Storytelling empresarial com foco em valores e cultura organizacional</p>
                        <div class="flex gap-2">
                            <span class="text-xs bg-white/10 text-white/80 px-2 py-1 rounded-full">Documentário</span>
                            <span class="text-xs bg-white/10 text-white/80 px-2 py-1 rounded-full">Corporativo</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="sobre" class="py-20 px-4 bg-gradient-to-b from-gray-900 to-black">
        <div class="max-w-6xl mx-auto">
            <div class="text-center mb-20">
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-8">
                    Sobre a MV Studio
                </h2>
                <div class="max-w-4xl mx-auto">
                    <p class="text-xl md:text-2xl text-white/80 leading-relaxed mb-8">
                        Somos mais que um estúdio audiovisual. Somos contadores de histórias, 
                        criadores de memórias e arquitetos de experiências visuais que tocam o coração.
                    </p>
                    <p class="text-lg text-white/70 leading-relaxed">
                        Na MV Studio, reunimos os melhores editores, filmmakers e fotógrafos num só lugar, 
                        unidos pela paixão de transformar ideias em experiências audiovisuais memoráveis.
                    </p>
                </div>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-20">
                <div class="glass text-center p-6 rounded-xl hover:bg-white/20 transition-colors">
                    <h3 class="text-2xl md:text-3xl font-bold text-white mb-2">500+</h3>
                    <p class="text-white/70 text-sm">Projetos Realizados</p>
                </div>
                <div class="glass text-center p-6 rounded-xl hover:bg-white/20 transition-colors">
                    <h3 class="text-2xl md:text-3xl font-bold text-white mb-2">200+</h3>
                    <p class="text-white/70 text-sm">Clientes Satisfeitos</p>
                </div>
                <div class="glass text-center p-6 rounded-xl hover:bg-white/20 transition-colors">
                    <h3 class="text-2xl md:text-3xl font-bold text-white mb-2">15+</h3>
                    <p class="text-white/70 text-sm">Prêmios Conquistados</p>
                </div>
                <div class="glass text-center p-6 rounded-xl hover:bg-white/20 transition-colors">
                    <h3 class="text-2xl md:text-3xl font-bold text-white mb-2">1000+</h3>
                    <p class="text-white/70 text-sm">Horas de Conteúdo</p>
                </div>
            </div>

            <div class="glass-dark rounded-xl p-8 md:p-12 text-center">
                <h3 class="text-3xl md:text-4xl font-bold text-white mb-8">Nossa Missão</h3>
                <p class="text-xl md:text-2xl text-white/80 leading-relaxed max-w-4xl mx-auto">
                    "Transformar momentos em eternidade, ideias em realidade visual, 
                    e sonhos em experiências que transcendem o tempo. Acreditamos que 
                    cada imagem tem o poder de contar uma história única e cada vídeo 
                    pode despertar emoções profundas."
                </p>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contato" class="py-20 px-4 bg-gradient-to-b from-black to-gray-900">
        <div class="max-w-4xl mx-auto text-center">
            <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">
                Vamos Falar
            </h2>
            <p class="text-xl text-white/70 mb-12">
                Pronto para transformar sua ideia em realidade? Entre em contato conosco e vamos criar algo extraordinário juntos.
            </p>
            
            <div class="glass rounded-xl p-8">
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <input type="text" placeholder="Seu nome" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50">
                        <input type="email" placeholder="<EMAIL>" class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50">
                    </div>
                    <select class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-white/50">
                        <option value="" class="bg-gray-800">Selecione um serviço</option>
                        <option value="fotografia" class="bg-gray-800">Fotografia de Eventos</option>
                        <option value="video" class="bg-gray-800">Produção de Vídeo</option>
                        <option value="corporativo" class="bg-gray-800">Fotografia Corporativa</option>
                    </select>
                    <textarea rows="5" placeholder="Conte-nos sobre seu projeto..." class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none"></textarea>
                    <button type="submit" class="w-full bg-white text-black px-8 py-4 rounded-lg hover:bg-white/90 transition-colors text-lg font-medium">
                        Enviar Mensagem
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gradient-to-t from-black to-gray-900 pt-20 pb-8">
        <div class="max-w-7xl mx-auto px-4">
            <div class="glass-dark rounded-xl p-8 md:p-12 mb-8">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <div class="lg:col-span-2">
                        <h3 class="text-3xl font-bold text-white mb-4">MV Studio</h3>
                        <p class="text-white/70 leading-relaxed mb-6 max-w-md">
                            Transformamos ideias em experiências audiovisuais memoráveis. 
                            Os melhores editores, filmmakers e fotógrafos num só lugar.
                        </p>
                        <div class="space-y-3 text-white/60 text-sm">
                            <div class="flex items-center space-x-2">
                                <span>📍</span>
                                <span>São Paulo, SP - Brasil</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span>📞</span>
                                <span>+55 (11) 99999-9999</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span>✉️</span>
                                <span><EMAIL></span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-white mb-4">Navegação</h4>
                        <ul class="space-y-2">
                            <li><a href="#inicio" class="text-white/60 hover:text-white transition-colors text-sm">Início</a></li>
                            <li><a href="#projetos" class="text-white/60 hover:text-white transition-colors text-sm">Projetos</a></li>
                            <li><a href="#albuns" class="text-white/60 hover:text-white transition-colors text-sm">Álbuns</a></li>
                            <li><a href="#sobre" class="text-white/60 hover:text-white transition-colors text-sm">Sobre</a></li>
                            <li><a href="#catalogos" class="text-white/60 hover:text-white transition-colors text-sm">Catálogos</a></li>
                            <li><a href="#blog" class="text-white/60 hover:text-white transition-colors text-sm">No Meu Mundo</a></li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-white mb-4">Serviços</h4>
                        <ul class="space-y-2">
                            <li><span class="text-white/60 text-sm">Fotografia de Eventos</span></li>
                            <li><span class="text-white/60 text-sm">Produção de Vídeo</span></li>
                            <li><span class="text-white/60 text-sm">Fotografia Corporativa</span></li>
                            <li><span class="text-white/60 text-sm">Clipes Musicais</span></li>
                            <li><span class="text-white/60 text-sm">Documentários</span></li>
                            <li><span class="text-white/60 text-sm">Reels para Redes Sociais</span></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-white/10 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center text-white/60 text-sm">
                    <p class="mb-4 md:mb-0">© 2024 MV Studio. Feito com ❤️ para criar experiências únicas.</p>
                    <div class="flex space-x-6">
                        <a href="#" class="hover:text-white transition-colors">Política de Privacidade</a>
                        <a href="#" class="hover:text-white transition-colors">Termos de Uso</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scroll to Top Button -->
    <button onclick="window.scrollTo({top: 0, behavior: 'smooth'})" class="fixed bottom-8 right-8 glass rounded-full p-3 text-white hover:bg-white/20 transition-all duration-300 z-40">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"/>
        </svg>
    </button>
</body>
</html>
