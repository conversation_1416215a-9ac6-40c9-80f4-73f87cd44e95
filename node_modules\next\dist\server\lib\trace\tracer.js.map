{"version": 3, "sources": ["../../../../src/server/lib/trace/tracer.ts"], "names": ["getTracer", "SpanStatusCode", "SpanKind", "api", "process", "env", "NEXT_RUNTIME", "require", "err", "context", "trace", "isPromise", "p", "then", "closeSpanWithError", "span", "error", "bubble", "setAttribute", "recordException", "setStatus", "code", "ERROR", "message", "end", "rootSpanAttributesStore", "Map", "rootSpanIdKey", "createContextKey", "lastSpanId", "getSpanId", "NextTracerImpl", "getTracerInstance", "getContext", "getActiveScopeSpan", "getSpan", "active", "args", "type", "fnOrOptions", "fnOrEmpty", "fn", "options", "NextVanillaSpanAllowlist", "includes", "NEXT_OTEL_VERBOSE", "hideSpan", "spanName", "spanContext", "getSpanContext", "parentSpan", "isRootSpan", "ROOT_CONTEXT", "spanId", "attributes", "with", "setValue", "startActiveSpan", "onCleanup", "delete", "set", "Object", "entries", "length", "result", "finally", "wrap", "tracer", "name", "optionsObj", "apply", "arguments", "lastArgId", "cb", "scopeBoundCb", "bind", "_span", "done", "startSpan", "setSpan", "undefined", "getRootSpanAttributes", "getValue", "get"], "mappings": ";;;;;;;;;;;;;;;;IAmXSA,SAAS;eAATA;;IAAWC,cAAc;eAAdA;;IAAgBC,QAAQ;eAARA;;;2BAlXK;AAUzC,IAAIC;AAEJ,gFAAgF;AAChF,8EAA8E;AAC9E,uCAAuC;AACvC,0EAA0E;AAC1E,+EAA+E;AAC/E,4CAA4C;AAC5C,6CAA6C;AAC7C,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvCH,MAAMI,QAAQ;AAChB,OAAO;IACL,IAAI;QACFJ,MAAMI,QAAQ;IAChB,EAAE,OAAOC,KAAK;QACZL,MAAMI,QAAQ;IAChB;AACF;AAEA,MAAM,EAAEE,OAAO,EAAEC,KAAK,EAAET,cAAc,EAAEC,QAAQ,EAAE,GAAGC;AAErD,MAAMQ,YAAY,CAAIC;IACpB,OAAOA,MAAM,QAAQ,OAAOA,MAAM,YAAY,OAAOA,EAAEC,IAAI,KAAK;AAClE;AAIA,MAAMC,qBAAqB,CAACC,MAAYC;IACtC,IAAI,CAACA,yBAAD,AAACA,MAAoCC,MAAM,MAAK,MAAM;QACxDF,KAAKG,YAAY,CAAC,eAAe;IACnC,OAAO;QACL,IAAIF,OAAO;YACTD,KAAKI,eAAe,CAACH;QACvB;QACAD,KAAKK,SAAS,CAAC;YAAEC,MAAMpB,eAAeqB,KAAK;YAAEC,OAAO,EAAEP,yBAAAA,MAAOO,OAAO;QAAC;IACvE;IACAR,KAAKS,GAAG;AACV;AAkGA,8EAA8E,GAC9E,MAAMC,0BAA0B,IAAIC;AAIpC,MAAMC,gBAAgBxB,IAAIyB,gBAAgB,CAAC;AAC3C,IAAIC,aAAa;AACjB,MAAMC,YAAY,IAAMD;AAExB,MAAME;IACJ;;;;GAIC,GACD,AAAQC,oBAA4B;QAClC,OAAOtB,MAAMV,SAAS,CAAC,WAAW;IACpC;IAEOiC,aAAyB;QAC9B,OAAOxB;IACT;IAEOyB,qBAAuC;QAC5C,OAAOxB,MAAMyB,OAAO,CAAC1B,2BAAAA,QAAS2B,MAAM;IACtC;IAsBO1B,MAAS,GAAG2B,IAAgB,EAAE;QACnC,MAAM,CAACC,MAAMC,aAAaC,UAAU,GAAGH;QAEvC,+BAA+B;QAC/B,MAAM,EACJI,EAAE,EACFC,OAAO,EACR,GAIC,OAAOH,gBAAgB,aACnB;YACEE,IAAIF;YACJG,SAAS,CAAC;QACZ,IACA;YACED,IAAID;YACJE,SAAS;gBAAE,GAAGH,WAAW;YAAC;QAC5B;QAEN,IACE,AAAC,CAACI,mCAAwB,CAACC,QAAQ,CAACN,SAClClC,QAAQC,GAAG,CAACwC,iBAAiB,KAAK,OACpCH,QAAQI,QAAQ,EAChB;YACA,OAAOL;QACT;QAEA,MAAMM,WAAWL,QAAQK,QAAQ,IAAIT;QAErC,mHAAmH;QACnH,IAAIU,cAAc,IAAI,CAACC,cAAc,CACnCP,CAAAA,2BAAAA,QAASQ,UAAU,KAAI,IAAI,CAAChB,kBAAkB;QAEhD,IAAIiB,aAAa;QAEjB,IAAI,CAACH,aAAa;YAChBA,cAAc7C,IAAIiD,YAAY;YAC9BD,aAAa;QACf;QAEA,MAAME,SAASvB;QAEfY,QAAQY,UAAU,GAAG;YACnB,kBAAkBP;YAClB,kBAAkBT;YAClB,GAAGI,QAAQY,UAAU;QACvB;QAEA,OAAOnD,IAAIM,OAAO,CAAC8C,IAAI,CAACP,YAAYQ,QAAQ,CAAC7B,eAAe0B,SAAS,IACnE,IAAI,CAACrB,iBAAiB,GAAGyB,eAAe,CACtCV,UACAL,SACA,CAAC3B;gBACC,MAAM2C,YAAY;oBAChBjC,wBAAwBkC,MAAM,CAACN;gBACjC;gBACA,IAAIF,YAAY;oBACd1B,wBAAwBmC,GAAG,CACzBP,QACA,IAAI3B,IACFmC,OAAOC,OAAO,CAACpB,QAAQY,UAAU,IAAI,CAAC;gBAM5C;gBACA,IAAI;oBACF,IAAIb,GAAGsB,MAAM,GAAG,GAAG;wBACjB,OAAOtB,GAAG1B,MAAM,CAACP,MAAgBM,mBAAmBC,MAAMP;oBAC5D;oBAEA,MAAMwD,SAASvB,GAAG1B;oBAElB,IAAIJ,UAAUqD,SAAS;wBACrBA,OACGnD,IAAI,CACH,IAAME,KAAKS,GAAG,IACd,CAAChB,MAAQM,mBAAmBC,MAAMP,MAEnCyD,OAAO,CAACP;oBACb,OAAO;wBACL3C,KAAKS,GAAG;wBACRkC;oBACF;oBAEA,OAAOM;gBACT,EAAE,OAAOxD,KAAU;oBACjBM,mBAAmBC,MAAMP;oBACzBkD;oBACA,MAAMlD;gBACR;YACF;IAGN;IAaO0D,KAAK,GAAG7B,IAAgB,EAAE;QAC/B,MAAM8B,SAAS,IAAI;QACnB,MAAM,CAACC,MAAM1B,SAASD,GAAG,GACvBJ,KAAK0B,MAAM,KAAK,IAAI1B,OAAO;YAACA,IAAI,CAAC,EAAE;YAAE,CAAC;YAAGA,IAAI,CAAC,EAAE;SAAC;QAEnD,IACE,CAACM,mCAAwB,CAACC,QAAQ,CAACwB,SACnChE,QAAQC,GAAG,CAACwC,iBAAiB,KAAK,KAClC;YACA,OAAOJ;QACT;QAEA,OAAO;YACL,IAAI4B,aAAa3B;YACjB,IAAI,OAAO2B,eAAe,cAAc,OAAO5B,OAAO,YAAY;gBAChE4B,aAAaA,WAAWC,KAAK,CAAC,IAAI,EAAEC;YACtC;YAEA,MAAMC,YAAYD,UAAUR,MAAM,GAAG;YACrC,MAAMU,KAAKF,SAAS,CAACC,UAAU;YAE/B,IAAI,OAAOC,OAAO,YAAY;gBAC5B,MAAMC,eAAeP,OAAOlC,UAAU,GAAG0C,IAAI,CAAClE,QAAQ2B,MAAM,IAAIqC;gBAChE,OAAON,OAAOzD,KAAK,CAAC0D,MAAMC,YAAY,CAACO,OAAOC;oBAC5CN,SAAS,CAACC,UAAU,GAAG,SAAUhE,GAAQ;wBACvCqE,wBAAAA,KAAOrE;wBACP,OAAOkE,aAAaJ,KAAK,CAAC,IAAI,EAAEC;oBAClC;oBAEA,OAAO9B,GAAG6B,KAAK,CAAC,IAAI,EAAEC;gBACxB;YACF,OAAO;gBACL,OAAOJ,OAAOzD,KAAK,CAAC0D,MAAMC,YAAY,IAAM5B,GAAG6B,KAAK,CAAC,IAAI,EAAEC;YAC7D;QACF;IACF;IAIOO,UAAU,GAAGzC,IAAgB,EAAQ;QAC1C,MAAM,CAACC,MAAMI,QAAQ,GAA4CL;QAEjE,MAAMW,cAAc,IAAI,CAACC,cAAc,CACrCP,CAAAA,2BAAAA,QAASQ,UAAU,KAAI,IAAI,CAAChB,kBAAkB;QAEhD,OAAO,IAAI,CAACF,iBAAiB,GAAG8C,SAAS,CAACxC,MAAMI,SAASM;IAC3D;IAEQC,eAAeC,UAAiB,EAAE;QACxC,MAAMF,cAAcE,aAChBxC,MAAMqE,OAAO,CAACtE,QAAQ2B,MAAM,IAAIc,cAChC8B;QAEJ,OAAOhC;IACT;IAEOiC,wBAAwB;QAC7B,MAAM5B,SAAS5C,QAAQ2B,MAAM,GAAG8C,QAAQ,CAACvD;QACzC,OAAOF,wBAAwB0D,GAAG,CAAC9B;IACrC;AACF;AAEA,MAAMrD,YAAY,AAAC,CAAA;IACjB,MAAMmE,SAAS,IAAIpC;IAEnB,OAAO,IAAMoC;AACf,CAAA"}