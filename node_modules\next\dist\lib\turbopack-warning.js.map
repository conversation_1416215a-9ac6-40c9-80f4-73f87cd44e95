{"version": 3, "sources": ["../../src/lib/turbopack-warning.ts"], "names": ["validateTurboNextConfig", "supportedTurbopackNextConfigOptions", "prodSpecificTurboNextConfigOptions", "dir", "isDev", "getPkgManager", "require", "getBabelConfigFile", "defaultConfig", "bold", "cyan", "red", "underline", "interopDefault", "unsupportedParts", "babelrc", "path", "basename", "hasWebpack", "hasTurbo", "process", "env", "TURBOPACK", "unsupportedConfig", "rawNextConfig", "loadConfig", "PHASE_DEVELOPMENT_SERVER", "rawConfig", "flatten<PERSON>eys", "obj", "prefix", "keys", "key", "pre", "length", "Array", "isArray", "concat", "push", "getDeepValue", "split", "slice", "customKeys", "<PERSON><PERSON><PERSON><PERSON>", "startsWith", "isSupported", "some", "<PERSON><PERSON><PERSON>", "e", "Log", "error", "feedbackMessage", "warn", "map", "name", "join", "pkgManager", "exit"], "mappings": ";;;;+BA8JsBA;;;eAAAA;;;6DA7JL;+DACM;6DACF;2BACoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEzC,MAAMC,sCAAsC;IAC1C,kCAAkC;IAClC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,0DAA0D;IAC1D;IACA;IACA;IACA;IACA;IACA;IAEA,+CAA+C;IAC/C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,qDAAqD;IACrD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA,oCAAoC;IACpC,kEAAkE;IAClE,yEAAyE;IACzE,4CAA4C;IAC5C;IACA;IACA,sBAAsB;IACtB,oCAAoC;IACpC,oBAAoB;IACpB,4BAA4B;IAC5B,+BAA+B;IAC/B,sCAAsC;IACtC,sCAAsC;IAEtC,yBAAyB;IACzB;IACA;IACA,gDAAgD;IAChD;IACA;CAsBD;AAED,kEAAkE;AAClE,MAAMC,qCAAqC;IACzC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,eAAeF,wBAAwB,EAC5CG,GAAG,EACHC,KAAK,EAON;IACC,MAAM,EAAEC,aAAa,EAAE,GACrBC,QAAQ;IACV,MAAM,EAAEC,kBAAkB,EAAE,GAC1BD,QAAQ;IACV,MAAM,EAAEE,aAAa,EAAE,GACrBF,QAAQ;IACV,MAAM,EAAEG,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,SAAS,EAAE,GAClCN,QAAQ;IACV,MAAM,EAAEO,cAAc,EAAE,GACtBP,QAAQ;IAEV,IAAIQ,mBAAmB;IACvB,IAAIC,UAAU,MAAMR,mBAAmBJ;IACvC,IAAIY,SAASA,UAAUC,aAAI,CAACC,QAAQ,CAACF;IAErC,IAAIG,aAAa;IACjB,IAAIC,WAAW,CAAC,CAACC,QAAQC,GAAG,CAACC,SAAS;IAEtC,IAAIC,oBAA8B,EAAE;IACpC,IAAIC,gBAA4B,CAAC;IAEjC,IAAI;QACFA,gBAAgBX,eACd,MAAMY,IAAAA,eAAU,EAACC,mCAAwB,EAAEvB,KAAK;YAC9CwB,WAAW;QACb;QAGF,IAAI,OAAOH,kBAAkB,YAAY;YACvCA,gBAAgB,AAACA,cAAsBE,mCAAwB,EAAE;gBAC/DlB;YACF;QACF;QAEA,MAAMoB,cAAc,CAACC,KAAUC,SAAiB,EAAE;YAChD,IAAIC,OAAiB,EAAE;YAEvB,IAAK,MAAMC,OAAOH,IAAK;gBACrB,IAAI,QAAOA,uBAAAA,GAAK,CAACG,IAAI,MAAK,aAAa;oBACrC;gBACF;gBAEA,MAAMC,MAAMH,OAAOI,MAAM,GAAG,CAAC,EAAEJ,OAAO,CAAC,CAAC,GAAG;gBAE3C,IACE,OAAOD,GAAG,CAACG,IAAI,KAAK,YACpB,CAACG,MAAMC,OAAO,CAACP,GAAG,CAACG,IAAI,KACvBH,GAAG,CAACG,IAAI,KAAK,MACb;oBACAD,OAAOA,KAAKM,MAAM,CAACT,YAAYC,GAAG,CAACG,IAAI,EAAEC,MAAMD;gBACjD,OAAO;oBACLD,KAAKO,IAAI,CAACL,MAAMD;gBAClB;YACF;YAEA,OAAOD;QACT;QAEA,MAAMQ,eAAe,CAACV,KAAUE;YAC9B,IAAI,OAAOA,SAAS,UAAU;gBAC5BA,OAAOA,KAAKS,KAAK,CAAC;YACpB;YACA,IAAIT,KAAKG,MAAM,KAAK,GAAG;gBACrB,OAAOL,uBAAAA,GAAK,CAACE,wBAAAA,IAAM,CAAC,EAAE,CAAC;YACzB;YACA,OAAOQ,aAAaV,uBAAAA,GAAK,CAACE,wBAAAA,IAAM,CAAC,EAAE,CAAC,EAAEA,KAAKU,KAAK,CAAC;QACnD;QAEA,MAAMC,aAAad,YAAYJ;QAE/B,IAAImB,gBAAgBvC,QAChB;eACKH;eACAC;SACJ,GACDD;QAEJ,KAAK,MAAM+B,OAAOU,WAAY;YAC5B,IAAIV,IAAIY,UAAU,CAAC,YAAY;gBAC7B1B,aAAa;YACf;YACA,IAAIc,IAAIY,UAAU,CAAC,uBAAuB;gBACxCzB,WAAW;YACb;YAEA,IAAI0B,cACFF,cAAcG,IAAI,CAAC,CAACC,eAAiBf,IAAIY,UAAU,CAACG,kBACpDR,aAAaf,eAAeQ,SAASO,aAAa/B,eAAewB;YACnE,IAAI,CAACa,aAAa;gBAChBtB,kBAAkBe,IAAI,CAACN;YACzB;QACF;IACF,EAAE,OAAOgB,GAAG;QACVC,KAAIC,KAAK,CAAC,mDAAmDF;IAC/D;IAEA,MAAMG,kBAAkB,CAAC,wCAAwC,EAAEvC,UACjE,sCACA,EAAE,CAAC;IAEL,IAAIM,cAAc,CAACC,UAAU;QAC3B8B,KAAIG,IAAI,CACN,CAAC,uEAAuE,CAAC;QAE3EH,KAAIG,IAAI,CACN,CAAC,sHAAsH,CAAC;IAE5H;IAEA,IAAIrC,SAAS;QACXD,oBAAoB,CAAC,gBAAgB,EAAEJ,KACrCK,SACA,8GAA8G,CAAC;IACnH;IAEA,IACEQ,kBAAkBW,MAAM,KAAK,KAC7BX,iBAAiB,CAAC,EAAE,KAAK,uCACzB;QACA0B,KAAIG,IAAI,CACN,CAAC,4FAA4F,CAAC;IAElG,OAAO,IAAI7B,kBAAkBW,MAAM,EAAE;QACnCpB,oBAAoB,CAAC,mDAAmD,EAAEJ,KACxE,kBACA,oEAAoE,EAAEa,kBACrE8B,GAAG,CAAC,CAACC,OAAS,CAAC,MAAM,EAAE3C,IAAI2C,MAAM,EAAE,CAAC,EACpCC,IAAI,CAAC,IAAI,CAAC;IACf;IAEA,IAAIzC,kBAAkB;QACpB,MAAM0C,aAAanD,cAAcF;QAEjC8C,KAAIC,KAAK,CACP,CAAC,iGAAiG,EAAEpC,iBAAiB;;;EAGzH,EAAEL,KACAC,KACE,CAAC,EACC8C,eAAe,QACX,wBACA,CAAC,EAAEA,WAAW,gBAAgB,CAAC,CACpC,4CAA4C,CAAC,GAEhD,6BAA6B,EAAEA,WAAW;QACtC,CAAC;QAGLP,KAAIG,IAAI,CAACD;QAET/B,QAAQqC,IAAI,CAAC;IACf;IAEA,OAAOjC;AACT"}