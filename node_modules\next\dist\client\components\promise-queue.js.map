{"version": 3, "sources": ["../../../src/client/components/promise-queue.ts"], "names": ["PromiseQueue", "enqueue", "promiseFn", "taskResolve", "taskReject", "taskPromise", "Promise", "resolve", "reject", "task", "runningCount", "result", "error", "processNext", "enqueueResult", "queue", "push", "bump", "index", "findIndex", "item", "bumpedItem", "splice", "unshift", "constructor", "maxConcurrency", "forced", "length", "shift"], "mappings": ";;;;+BAQaA;;;eAAAA;;;;;IACX,qFACA,iFACA,mEAmDA;AAtDK,MAAMA;IAcXC,QAAWC,SAA2B,EAAc;QAClD,IAAIC;QACJ,IAAIC;QAEJ,MAAMC,cAAc,IAAIC,QAAQ,CAACC,SAASC;YACxCL,cAAcI;YACdH,aAAaI;QACf;QAEA,MAAMC,OAAO;YACX,IAAI;gBACF,kCAAA,IAAI,EAAEC,eAAAA;gBACN,MAAMC,SAAS,MAAMT;gBACrBC,YAAYQ;YACd,EAAE,OAAOC,OAAO;gBACdR,WAAWQ;YACb,SAAU;gBACR,kCAAA,IAAI,EAAEF,eAAAA;gBACN,kCAAA,IAAI,EAAEG,cAAAA;YACR;QACF;QAEA,MAAMC,gBAAgB;YAAEZ,WAAWG;YAAaI;QAAK;QACrD,gDAAgD;QAChD,kCAAA,IAAI,EAAEM,QAAAA,QAAMC,IAAI,CAACF;QACjB,kCAAA,IAAI,EAAED,cAAAA;QAEN,OAAOR;IACT;IAEAY,KAAKf,SAA6C,EAAE;QAClD,MAAMgB,QAAQ,kCAAA,IAAI,EAAEH,QAAAA,QAAMI,SAAS,CAAC,CAACC,OAASA,KAAKlB,SAAS,KAAKA;QAEjE,IAAIgB,QAAQ,CAAC,GAAG;YACd,MAAMG,aAAa,kCAAA,IAAI,EAAEN,QAAAA,QAAMO,MAAM,CAACJ,OAAO,EAAE,CAAC,EAAE;YAClD,kCAAA,IAAI,EAAEH,QAAAA,QAAMQ,OAAO,CAACF;YACpB,kCAAA,IAAI,EAAER,cAAAA,cAAY;QACpB;IACF;IA5CAW,YAAYC,iBAAiB,CAAC,CAAE;QA8ChC,4BAAA;mBAAA;;QArDA,4BAAA;;mBAAA,KAAA;;QACA,4BAAA;;mBAAA,KAAA;;QACA,4BAAA;;mBAAA,KAAA;;QAME,kCAAA,IAAI,EAAEA,iBAAAA,mBAAiBA;QACvB,kCAAA,IAAI,EAAEf,eAAAA,iBAAe;QACrB,kCAAA,IAAI,EAAEK,QAAAA,UAAQ,EAAE;IAClB;AAkDF;AARE,SAAA,YAAaW,MAAc;IAAdA,IAAAA,mBAAAA,SAAS;IACpB,IACE,AAAC,CAAA,kCAAA,IAAI,EAAEhB,eAAAA,iBAAe,kCAAA,IAAI,EAAEe,iBAAAA,oBAAkBC,MAAK,KACnD,kCAAA,IAAI,EAAEX,QAAAA,QAAMY,MAAM,GAAG,GACrB;YACA;SAAA,+CAAA,kCAAA,IAAI,EAAEZ,QAAAA,QAAMa,KAAK,uBAAjB,6CAAqBnB,IAAI;IAC3B;AACF"}