{"version": 3, "sources": ["../../../../../src/server/future/normalizers/request/postponed.ts"], "names": ["PostponedPathnameNormalizer", "constructor", "ppr", "match", "pathname", "startsWith", "normalize", "matched", "substring", "length"], "mappings": ";;;;+BAEaA;;;eAAAA;;;AAAN,MAAMA;IACXC,YAA6BC,IAA0B;mBAA1BA;IAA2B;IAEjDC,MAAMC,QAAgB,EAAE;QAC7B,wCAAwC;QACxC,IAAI,CAAC,IAAI,CAACF,GAAG,EAAE,OAAO;QAEtB,iEAAiE;QACjE,IAAI,CAACE,SAASC,UAAU,CAAC,qBAAqB,OAAO;QAErD,OAAO;IACT;IAEOC,UAAUF,QAAgB,EAAEG,OAAiB,EAAU;QAC5D,oDAAoD;QACpD,IAAI,CAAC,IAAI,CAACL,GAAG,EAAE,OAAOE;QAEtB,uEAAuE;QACvE,IAAI,CAACG,WAAW,CAAC,IAAI,CAACJ,KAAK,CAACC,WAAW,OAAOA;QAE9C,qBAAqB;QACrB,OAAOA,SAASI,SAAS,CAAC,mBAAmBC,MAAM,KAAK;IAC1D;AACF"}