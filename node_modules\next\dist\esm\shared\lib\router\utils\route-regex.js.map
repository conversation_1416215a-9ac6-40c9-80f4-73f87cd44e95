{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/route-regex.ts"], "names": ["INTERCEPTION_ROUTE_MARKERS", "escapeStringRegexp", "removeTrailingSlash", "NEXT_QUERY_PARAM_PREFIX", "NEXT_INTERCEPTION_MARKER_PREFIX", "parseParameter", "param", "optional", "startsWith", "endsWith", "slice", "repeat", "key", "getParametrizedRoute", "route", "segments", "split", "groups", "groupIndex", "parameterizedRoute", "map", "segment", "markerMatch", "find", "m", "paramMatch<PERSON>", "match", "pos", "join", "getRouteRegex", "normalizedRoute", "re", "RegExp", "buildGetSafeRouteKey", "i", "routeKey", "j", "String", "fromCharCode", "Math", "floor", "getSafeKeyFromSegment", "getSafeRouteKey", "routeKeys", "keyPrefix", "<PERSON><PERSON><PERSON>", "replace", "<PERSON><PERSON><PERSON>", "length", "isNaN", "parseInt", "getNamedParametrizedRoute", "prefixRouteKeys", "namedParameterizedRoute", "hasInterceptionMarker", "some", "undefined", "getNamedRouteRegex", "prefixRouteKey", "result", "namedRegex", "getNamedMiddlewareRegex", "options", "catchAll", "catchAllRegex", "catchAllGroupedRegex"], "mappings": "AAAA,SAASA,0BAA0B,QAAQ,wDAAuD;AAClG,SAASC,kBAAkB,QAAQ,sBAAqB;AACxD,SAASC,mBAAmB,QAAQ,0BAAyB;AAE7D,MAAMC,0BAA0B;AAChC,MAAMC,kCAAkC;AAaxC;;;;;;;CAOC,GACD,SAASC,eAAeC,KAAa;IACnC,MAAMC,WAAWD,MAAME,UAAU,CAAC,QAAQF,MAAMG,QAAQ,CAAC;IACzD,IAAIF,UAAU;QACZD,QAAQA,MAAMI,KAAK,CAAC,GAAG,CAAC;IAC1B;IACA,MAAMC,SAASL,MAAME,UAAU,CAAC;IAChC,IAAIG,QAAQ;QACVL,QAAQA,MAAMI,KAAK,CAAC;IACtB;IACA,OAAO;QAAEE,KAAKN;QAAOK;QAAQJ;IAAS;AACxC;AAEA,SAASM,qBAAqBC,KAAa;IACzC,MAAMC,WAAWb,oBAAoBY,OAAOJ,KAAK,CAAC,GAAGM,KAAK,CAAC;IAC3D,MAAMC,SAAyC,CAAC;IAChD,IAAIC,aAAa;IACjB,OAAO;QACLC,oBAAoBJ,SACjBK,GAAG,CAAC,CAACC;YACJ,MAAMC,cAActB,2BAA2BuB,IAAI,CAAC,CAACC,IACnDH,QAAQb,UAAU,CAACgB;YAErB,MAAMC,eAAeJ,QAAQK,KAAK,CAAC,uBAAuB,uBAAuB;;YAEjF,IAAIJ,eAAeG,cAAc;gBAC/B,MAAM,EAAEb,GAAG,EAAEL,QAAQ,EAAEI,MAAM,EAAE,GAAGN,eAAeoB,YAAY,CAAC,EAAE;gBAChER,MAAM,CAACL,IAAI,GAAG;oBAAEe,KAAKT;oBAAcP;oBAAQJ;gBAAS;gBACpD,OAAO,AAAC,MAAGN,mBAAmBqB,eAAa;YAC7C,OAAO,IAAIG,cAAc;gBACvB,MAAM,EAAEb,GAAG,EAAED,MAAM,EAAEJ,QAAQ,EAAE,GAAGF,eAAeoB,YAAY,CAAC,EAAE;gBAChER,MAAM,CAACL,IAAI,GAAG;oBAAEe,KAAKT;oBAAcP;oBAAQJ;gBAAS;gBACpD,OAAOI,SAAUJ,WAAW,gBAAgB,WAAY;YAC1D,OAAO;gBACL,OAAO,AAAC,MAAGN,mBAAmBoB;YAChC;QACF,GACCO,IAAI,CAAC;QACRX;IACF;AACF;AAEA;;;;CAIC,GACD,OAAO,SAASY,cAAcC,eAAuB;IACnD,MAAM,EAAEX,kBAAkB,EAAEF,MAAM,EAAE,GAAGJ,qBAAqBiB;IAC5D,OAAO;QACLC,IAAI,IAAIC,OAAO,AAAC,MAAGb,qBAAmB;QACtCF,QAAQA;IACV;AACF;AAEA;;;CAGC,GACD,SAASgB;IACP,IAAIC,IAAI;IAER,OAAO;QACL,IAAIC,WAAW;QACf,IAAIC,IAAI,EAAEF;QACV,MAAOE,IAAI,EAAG;YACZD,YAAYE,OAAOC,YAAY,CAAC,KAAM,AAACF,CAAAA,IAAI,CAAA,IAAK;YAChDA,IAAIG,KAAKC,KAAK,CAAC,AAACJ,CAAAA,IAAI,CAAA,IAAK;QAC3B;QACA,OAAOD;IACT;AACF;AAEA,SAASM,sBAAsB,KAU9B;IAV8B,IAAA,EAC7BC,eAAe,EACfrB,OAAO,EACPsB,SAAS,EACTC,SAAS,EAMV,GAV8B;IAW7B,MAAM,EAAEhC,GAAG,EAAEL,QAAQ,EAAEI,MAAM,EAAE,GAAGN,eAAegB;IAEjD,uDAAuD;IACvD,kBAAkB;IAClB,IAAIwB,aAAajC,IAAIkC,OAAO,CAAC,OAAO;IAEpC,IAAIF,WAAW;QACbC,aAAa,AAAC,KAAED,YAAYC;IAC9B;IACA,IAAIE,aAAa;IAEjB,kEAAkE;IAClE,WAAW;IACX,IAAIF,WAAWG,MAAM,KAAK,KAAKH,WAAWG,MAAM,GAAG,IAAI;QACrDD,aAAa;IACf;IACA,IAAI,CAACE,MAAMC,SAASL,WAAWnC,KAAK,CAAC,GAAG,MAAM;QAC5CqC,aAAa;IACf;IAEA,IAAIA,YAAY;QACdF,aAAaH;IACf;IAEA,IAAIE,WAAW;QACbD,SAAS,CAACE,WAAW,GAAG,AAAC,KAAED,YAAYhC;IACzC,OAAO;QACL+B,SAAS,CAACE,WAAW,GAAG,AAAC,KAAEjC;IAC7B;IAEA,OAAOD,SACHJ,WACE,AAAC,YAASsC,aAAW,YACrB,AAAC,SAAMA,aAAW,UACpB,AAAC,SAAMA,aAAW;AACxB;AAEA,SAASM,0BAA0BrC,KAAa,EAAEsC,eAAwB;IACxE,MAAMrC,WAAWb,oBAAoBY,OAAOJ,KAAK,CAAC,GAAGM,KAAK,CAAC;IAC3D,MAAM0B,kBAAkBT;IACxB,MAAMU,YAAyC,CAAC;IAChD,OAAO;QACLU,yBAAyBtC,SACtBK,GAAG,CAAC,CAACC;YACJ,MAAMiC,wBAAwBtD,2BAA2BuD,IAAI,CAAC,CAAC/B,IAC7DH,QAAQb,UAAU,CAACgB;YAErB,MAAMC,eAAeJ,QAAQK,KAAK,CAAC,uBAAuB,uBAAuB;;YAEjF,IAAI4B,yBAAyB7B,cAAc;gBACzC,OAAOgB,sBAAsB;oBAC3BC;oBACArB,SAASI,YAAY,CAAC,EAAE;oBACxBkB;oBACAC,WAAWQ,kBACPhD,kCACAoD;gBACN;YACF,OAAO,IAAI/B,cAAc;gBACvB,OAAOgB,sBAAsB;oBAC3BC;oBACArB,SAASI,YAAY,CAAC,EAAE;oBACxBkB;oBACAC,WAAWQ,kBAAkBjD,0BAA0BqD;gBACzD;YACF,OAAO;gBACL,OAAO,AAAC,MAAGvD,mBAAmBoB;YAChC;QACF,GACCO,IAAI,CAAC;QACRe;IACF;AACF;AAEA;;;;;;;CAOC,GACD,OAAO,SAASc,mBACd3B,eAAuB,EACvB4B,cAAuB;IAEvB,MAAMC,SAASR,0BAA0BrB,iBAAiB4B;IAC1D,OAAO;QACL,GAAG7B,cAAcC,gBAAgB;QACjC8B,YAAY,AAAC,MAAGD,OAAON,uBAAuB,GAAC;QAC/CV,WAAWgB,OAAOhB,SAAS;IAC7B;AACF;AAEA;;;CAGC,GACD,OAAO,SAASkB,wBACd/B,eAAuB,EACvBgC,OAEC;IAED,MAAM,EAAE3C,kBAAkB,EAAE,GAAGN,qBAAqBiB;IACpD,MAAM,EAAEiC,WAAW,IAAI,EAAE,GAAGD;IAC5B,IAAI3C,uBAAuB,KAAK;QAC9B,IAAI6C,gBAAgBD,WAAW,OAAO;QACtC,OAAO;YACLH,YAAY,AAAC,OAAII,gBAAc;QACjC;IACF;IAEA,MAAM,EAAEX,uBAAuB,EAAE,GAAGF,0BAClCrB,iBACA;IAEF,IAAImC,uBAAuBF,WAAW,eAAe;IACrD,OAAO;QACLH,YAAY,AAAC,MAAGP,0BAA0BY,uBAAqB;IACjE;AACF"}