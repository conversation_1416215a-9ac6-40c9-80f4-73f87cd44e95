{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/refresh-reducer.ts"], "names": ["refreshReducer", "state", "action", "cache", "mutable", "origin", "href", "canonicalUrl", "currentTree", "tree", "isForCurrentTree", "JSON", "stringify", "previousTree", "handleMutable", "data", "createRecordFromThenable", "fetchServerResponse", "URL", "nextUrl", "buildId", "flightData", "canonicalUrlOverride", "readRecordValue", "handleExternalUrl", "pushRef", "pendingPush", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "applyRouterStatePatchToTree", "Error", "isNavigatingToNewRootLayout", "canonicalUrlOverrideHref", "createHrefFromUrl", "undefined", "subTreeData", "head", "slice", "status", "CacheStates", "READY", "fillLazyItemsTillLeafWithHead", "prefetchCache", "Map", "patchedTree"], "mappings": ";;;;+BAgBgBA;;;eAAAA;;;qCAhBoB;0CACK;iCACT;mCACE;6CACU;6CACA;iCAMV;+BACJ;+CACF;+CACkB;AAEvC,SAASA,eACdC,KAA2B,EAC3BC,MAAqB;IAErB,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,MAAM,EAAE,GAAGH;IACnC,MAAMI,OAAOL,MAAMM,YAAY;IAE/B,IAAIC,cAAcP,MAAMQ,IAAI;IAE5B,MAAMC,mBACJC,KAAKC,SAAS,CAACR,QAAQS,YAAY,MAAMF,KAAKC,SAAS,CAACJ;IAE1D,IAAIE,kBAAkB;QACpB,OAAOI,IAAAA,4BAAa,EAACb,OAAOG;IAC9B;IAEA,IAAI,CAACD,MAAMY,IAAI,EAAE;QACf,uDAAuD;QACvD,wCAAwC;QACxCZ,MAAMY,IAAI,GAAGC,IAAAA,kDAAwB,EACnCC,IAAAA,wCAAmB,EACjB,IAAIC,IAAIZ,MAAMD,SACd;YAACG,WAAW,CAAC,EAAE;YAAEA,WAAW,CAAC,EAAE;YAAEA,WAAW,CAAC,EAAE;YAAE;SAAU,EAC3DP,MAAMkB,OAAO,EACblB,MAAMmB,OAAO;IAGnB;IACA,MAAM,CAACC,YAAYC,qBAAqB,GAAGC,IAAAA,gCAAe,EAACpB,MAAMY,IAAI;IAErE,4DAA4D;IAC5D,IAAI,OAAOM,eAAe,UAAU;QAClC,OAAOG,IAAAA,kCAAiB,EACtBvB,OACAG,SACAiB,YACApB,MAAMwB,OAAO,CAACC,WAAW;IAE7B;IAEA,2DAA2D;IAC3DvB,MAAMY,IAAI,GAAG;IAEb,KAAK,MAAMY,kBAAkBN,WAAY;QACvC,oFAAoF;QACpF,IAAIM,eAAeC,MAAM,KAAK,GAAG;YAC/B,oCAAoC;YACpCC,QAAQC,GAAG,CAAC;YACZ,OAAO7B;QACT;QAEA,2GAA2G;QAC3G,MAAM,CAAC8B,UAAU,GAAGJ;QACpB,MAAMK,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;QACtB;YAAC;SAAG,EACJzB,aACAuB;QAGF,IAAIC,YAAY,MAAM;YACpB,MAAM,IAAIE,MAAM;QAClB;QAEA,IAAIC,IAAAA,wDAA2B,EAAC3B,aAAawB,UAAU;YACrD,OAAOR,IAAAA,kCAAiB,EAACvB,OAAOG,SAASE,MAAML,MAAMwB,OAAO,CAACC,WAAW;QAC1E;QAEA,MAAMU,2BAA2Bd,uBAC7Be,IAAAA,oCAAiB,EAACf,wBAClBgB;QAEJ,IAAIhB,sBAAsB;YACxBlB,QAAQG,YAAY,GAAG6B;QACzB;QAEA,0DAA0D;QAC1D,MAAM,CAACG,aAAaC,KAAK,GAAGb,eAAec,KAAK,CAAC,CAAC;QAElD,8FAA8F;QAC9F,IAAIF,gBAAgB,MAAM;YACxBpC,MAAMuC,MAAM,GAAGC,0CAAW,CAACC,KAAK;YAChCzC,MAAMoC,WAAW,GAAGA;YACpBM,IAAAA,4DAA6B,EAC3B1C,OACA,4FAA4F;YAC5FmC,WACAP,WACAS;YAEFpC,QAAQD,KAAK,GAAGA;YAChBC,QAAQ0C,aAAa,GAAG,IAAIC;QAC9B;QAEA3C,QAAQS,YAAY,GAAGL;QACvBJ,QAAQ4C,WAAW,GAAGhB;QACtB5B,QAAQG,YAAY,GAAGD;QAEvBE,cAAcwB;IAChB;IAEA,OAAOlB,IAAAA,4BAAa,EAACb,OAAOG;AAC9B"}