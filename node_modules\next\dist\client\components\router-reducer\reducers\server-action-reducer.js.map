{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-action-reducer.ts"], "names": ["serverActionReducer", "createFromFetch", "encodeReply", "process", "env", "NEXT_RUNTIME", "require", "fetchServerAction", "state", "actionId", "actionArgs", "body", "res", "fetch", "method", "headers", "Accept", "RSC_CONTENT_TYPE_HEADER", "ACTION", "NEXT_ROUTER_STATE_TREE", "encodeURIComponent", "JSON", "stringify", "tree", "__NEXT_ACTIONS_DEPLOYMENT_ID", "NEXT_DEPLOYMENT_ID", "nextUrl", "NEXT_URL", "location", "get", "revalidatedParts", "revalidatedHeader", "parse", "paths", "tag", "cookie", "e", "redirectLocation", "URL", "addBasePath", "canonicalUrl", "window", "href", "undefined", "isFlightResponse", "response", "Promise", "resolve", "callServer", "actionFlightData", "actionResult", "action", "mutable", "cache", "reject", "currentTree", "isForCurrentTree", "previousTree", "handleMutable", "inFlightServerAction", "status", "globalMutable", "pendingNavigatePath", "then", "actionResultResolved", "refresh", "createRecordFromThenable", "flightData", "readRecordValue", "pushRef", "pendingPush", "handleExternalUrl", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "applyRouterStatePatchToTree", "Error", "isNavigatingToNewRootLayout", "subTreeData", "head", "slice", "CacheStates", "READY", "fillLazyItemsTillLeafWithHead", "prefetchCache", "Map", "patchedTree", "newHref", "createHrefFromUrl", "reason"], "mappings": ";;;;+BAiJg<PERSON>;;;eAAAA;;;+BA5IW;kCAMpB;0CACkC;iCACT;6BAkBJ;mCACM;iCACA;6CACU;6CACA;+CAChB;+BACE;+CACgB;AAxB9C,gEAAgE;AAChE,oEAAoE;AACpE,gEAAgE;AAChE,gEAAgE;AAChE,MAAM,EAAEC,eAAe,EAAEC,WAAW,EAAE,GACpC,CAAC,CAACC,QAAQC,GAAG,CAACC,YAAY,GAEtBC,QAAQ,0CAERA,QAAQ;AA4Bd,eAAeC,kBACbC,KAA2B,EAC3B,KAA4C;IAA5C,IAAA,EAAEC,QAAQ,EAAEC,UAAU,EAAsB,GAA5C;IAEA,MAAMC,OAAO,MAAMT,YAAYQ;IAE/B,MAAME,MAAM,MAAMC,MAAM,IAAI;QAC1BC,QAAQ;QACRC,SAAS;YACPC,QAAQC,yCAAuB;YAC/B,CAACC,wBAAM,CAAC,EAAET;YACV,CAACU,wCAAsB,CAAC,EAAEC,mBAAmBC,KAAKC,SAAS,CAACd,MAAMe,IAAI;YACtE,GAAIpB,QAAQC,GAAG,CAACoB,4BAA4B,IAC5CrB,QAAQC,GAAG,CAACqB,kBAAkB,GAC1B;gBACE,mBAAmBtB,QAAQC,GAAG,CAACqB,kBAAkB;YACnD,IACA,CAAC,CAAC;YACN,GAAIjB,MAAMkB,OAAO,GACb;gBACE,CAACC,0BAAQ,CAAC,EAAEnB,MAAMkB,OAAO;YAC3B,IACA,CAAC,CAAC;QACR;QACAf;IACF;IAEA,MAAMiB,WAAWhB,IAAIG,OAAO,CAACc,GAAG,CAAC;IACjC,IAAIC;IACJ,IAAI;QACF,MAAMC,oBAAoBV,KAAKW,KAAK,CAClCpB,IAAIG,OAAO,CAACc,GAAG,CAAC,2BAA2B;QAE7CC,mBAAmB;YACjBG,OAAOF,iBAAiB,CAAC,EAAE,IAAI,EAAE;YACjCG,KAAK,CAAC,CAACH,iBAAiB,CAAC,EAAE;YAC3BI,QAAQJ,iBAAiB,CAAC,EAAE;QAC9B;IACF,EAAE,OAAOK,GAAG;QACVN,mBAAmB;YACjBG,OAAO,EAAE;YACTC,KAAK;YACLC,QAAQ;QACV;IACF;IAEA,MAAME,mBAAmBT,WACrB,IAAIU,IACFC,IAAAA,wBAAW,EAACX,WACZ,sFAAsF;IACtF,IAAIU,IAAI9B,MAAMgC,YAAY,EAAEC,OAAOb,QAAQ,CAACc,IAAI,KAElDC;IAEJ,IAAIC,mBACFhC,IAAIG,OAAO,CAACc,GAAG,CAAC,oBAAoBZ,yCAAuB;IAE7D,IAAI2B,kBAAkB;QACpB,MAAMC,WAAiC,MAAM5C,gBAC3C6C,QAAQC,OAAO,CAACnC,MAChB;YACEoC,YAAAA,yBAAU;QACZ;QAGF,IAAIpB,UAAU;YACZ,qEAAqE;YACrE,MAAM,GAAGqB,iBAAiB,GAAG,AAACJ,mBAAAA,WAAoB,EAAE;YACpD,OAAO;gBACLI,kBAAkBA;gBAClBZ;gBACAP;YACF;QACF;QAEA,6DAA6D;QAC7D,MAAM,CAACoB,cAAc,GAAGD,iBAAiB,CAAC,GAAG,AAACJ,mBAAAA,WAAoB,EAAE;QACpE,OAAO;YACLK;YACAD;YACAZ;YACAP;QACF;IACF;IACA,OAAO;QACLO;QACAP;IACF;AACF;AAMO,SAAS9B,oBACdQ,KAA2B,EAC3B2C,MAA0B;IAE1B,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEN,OAAO,EAAEO,MAAM,EAAE,GAAGH;IAC5C,MAAMT,OAAOlC,MAAMgC,YAAY;IAE/B,IAAIe,cAAc/C,MAAMe,IAAI;IAE5B,MAAMiC,mBACJnC,KAAKC,SAAS,CAAC8B,QAAQK,YAAY,MAAMpC,KAAKC,SAAS,CAACiC;IAE1D,IAAIC,kBAAkB;QACpB,OAAOE,IAAAA,4BAAa,EAAClD,OAAO4C;IAC9B;IAEA,IAAIA,QAAQO,oBAAoB,EAAE;QAChC,8CAA8C;QAC9C,qCAAqC;QACrC,IACEP,QAAQO,oBAAoB,CAACC,MAAM,KAAK,eACxCR,QAAQS,aAAa,CAACC,mBAAmB,IACzCV,QAAQS,aAAa,CAACC,mBAAmB,KAAKpB,MAC9C;YACAU,QAAQO,oBAAoB,CAACI,IAAI,CAC/B;gBACE,IAAIX,QAAQY,oBAAoB,EAAE;gBAElC,+DAA+D;gBAC/D,2FAA2F;gBAC3FZ,QAAQO,oBAAoB,GAAG;gBAC/BP,QAAQS,aAAa,CAACC,mBAAmB,GAAGnB;gBAC5CS,QAAQS,aAAa,CAACI,OAAO;gBAC7Bb,QAAQY,oBAAoB,GAAG;YACjC,GACA,KAAO;YAGT,OAAOxD;QACT;IACF,OAAO;QACL4C,QAAQO,oBAAoB,GAAGO,IAAAA,kDAAwB,EACrD3D,kBAAkBC,OAAO2C;IAE7B;IAEA,iHAAiH;IACjH,IAAI;QACF,gDAAgD;QAChD,MAAM,EACJD,YAAY,EACZD,kBAAkBkB,UAAU,EAC5B9B,gBAAgB,EAEjB,GAAG+B,IAAAA,gCAAe,EACjBhB,QAAQO,oBAAoB;QAG9B,4DAA4D;QAC5D,wDAAwD;QACxD,IAAItB,kBAAkB;YACpB7B,MAAM6D,OAAO,CAACC,WAAW,GAAG;YAC5BlB,QAAQkB,WAAW,GAAG;QACxB;QAEAlB,QAAQK,YAAY,GAAGjD,MAAMe,IAAI;QAEjC,IAAI,CAAC4C,YAAY;YACf,IAAI,CAACf,QAAQY,oBAAoB,EAAE;gBACjCjB,QAAQG;gBACRE,QAAQY,oBAAoB,GAAG;YACjC;YAEA,2EAA2E;YAC3E,IAAI3B,kBAAkB;gBACpB,OAAOkC,IAAAA,kCAAiB,EACtB/D,OACA4C,SACAf,iBAAiBK,IAAI,EACrBlC,MAAM6D,OAAO,CAACC,WAAW;YAE7B;YACA,OAAO9D;QACT;QAEA,IAAI,OAAO2D,eAAe,UAAU;YAClC,4DAA4D;YAC5D,OAAOI,IAAAA,kCAAiB,EACtB/D,OACA4C,SACAe,YACA3D,MAAM6D,OAAO,CAACC,WAAW;QAE7B;QAEA,2DAA2D;QAC3DlB,QAAQO,oBAAoB,GAAG;QAE/B,KAAK,MAAMa,kBAAkBL,WAAY;YACvC,oFAAoF;YACpF,IAAIK,eAAeC,MAAM,KAAK,GAAG;gBAC/B,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAOnE;YACT;YAEA,2GAA2G;YAC3G,MAAM,CAACoE,UAAU,GAAGJ;YACpB,MAAMK,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;YACtB;gBAAC;aAAG,EACJvB,aACAqB;YAGF,IAAIC,YAAY,MAAM;gBACpB,MAAM,IAAIE,MAAM;YAClB;YAEA,IAAIC,IAAAA,wDAA2B,EAACzB,aAAasB,UAAU;gBACrD,OAAON,IAAAA,kCAAiB,EACtB/D,OACA4C,SACAV,MACAlC,MAAM6D,OAAO,CAACC,WAAW;YAE7B;YAEA,0DAA0D;YAC1D,MAAM,CAACW,aAAaC,KAAK,GAAGV,eAAeW,KAAK,CAAC,CAAC;YAElD,8FAA8F;YAC9F,IAAIF,gBAAgB,MAAM;gBACxB5B,MAAMO,MAAM,GAAGwB,0CAAW,CAACC,KAAK;gBAChChC,MAAM4B,WAAW,GAAGA;gBACpBK,IAAAA,4DAA6B,EAC3BjC,OACA,4FAA4F;gBAC5FV,WACAiC,WACAM;gBAEF9B,QAAQC,KAAK,GAAGA;gBAChBD,QAAQmC,aAAa,GAAG,IAAIC;YAC9B;YAEApC,QAAQK,YAAY,GAAGF;YACvBH,QAAQqC,WAAW,GAAGZ;YACtBzB,QAAQZ,YAAY,GAAGE;YAEvBa,cAAcsB;QAChB;QAEA,IAAIxC,kBAAkB;YACpB,MAAMqD,UAAUC,IAAAA,oCAAiB,EAACtD,kBAAkB;YACpDe,QAAQZ,YAAY,GAAGkD;QACzB;QAEA,IAAI,CAACtC,QAAQY,oBAAoB,EAAE;YACjCjB,QAAQG;YACRE,QAAQY,oBAAoB,GAAG;QACjC;QACA,OAAON,IAAAA,4BAAa,EAAClD,OAAO4C;IAC9B,EAAE,OAAOhB,GAAQ;QACf,IAAIA,EAAEwB,MAAM,KAAK,YAAY;YAC3B,IAAI,CAACR,QAAQY,oBAAoB,EAAE;gBACjCV,OAAOlB,EAAEwD,MAAM;gBACfxC,QAAQY,oBAAoB,GAAG;YACjC;YAEA,mHAAmH;YACnH,OAAOxD;QACT;QAEA,MAAM4B;IACR;AACF"}