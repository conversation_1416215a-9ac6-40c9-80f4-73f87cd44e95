{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/route-regex.ts"], "names": ["getRouteRegex", "getNamedRouteRegex", "getNamedMiddlewareRegex", "NEXT_QUERY_PARAM_PREFIX", "NEXT_INTERCEPTION_MARKER_PREFIX", "parseParameter", "param", "optional", "startsWith", "endsWith", "slice", "repeat", "key", "getParametrizedRoute", "route", "segments", "removeTrailingSlash", "split", "groups", "groupIndex", "parameterizedRoute", "map", "segment", "markerMatch", "INTERCEPTION_ROUTE_MARKERS", "find", "m", "paramMatch<PERSON>", "match", "pos", "escapeStringRegexp", "join", "normalizedRoute", "re", "RegExp", "buildGetSafeRouteKey", "i", "routeKey", "j", "String", "fromCharCode", "Math", "floor", "getSafeKeyFromSegment", "getSafeRouteKey", "routeKeys", "keyPrefix", "<PERSON><PERSON><PERSON>", "replace", "<PERSON><PERSON><PERSON>", "length", "isNaN", "parseInt", "getNamedParametrizedRoute", "prefixRouteKeys", "namedParameterizedRoute", "hasInterceptionMarker", "some", "undefined", "prefixRouteKey", "result", "namedRegex", "options", "catchAll", "catchAllRegex", "catchAllGroupedRegex"], "mappings": ";;;;;;;;;;;;;;;;IAwEgBA,aAAa;eAAbA;;IAuHAC,kBAAkB;eAAlBA;;IAgBAC,uBAAuB;eAAvBA;;;oCA/M2B;8BACR;qCACC;AAEpC,MAAMC,0BAA0B;AAChC,MAAMC,kCAAkC;AAaxC;;;;;;;CAOC,GACD,SAASC,eAAeC,KAAa;IACnC,MAAMC,WAAWD,MAAME,UAAU,CAAC,QAAQF,MAAMG,QAAQ,CAAC;IACzD,IAAIF,UAAU;QACZD,QAAQA,MAAMI,KAAK,CAAC,GAAG,CAAC;IAC1B;IACA,MAAMC,SAASL,MAAME,UAAU,CAAC;IAChC,IAAIG,QAAQ;QACVL,QAAQA,MAAMI,KAAK,CAAC;IACtB;IACA,OAAO;QAAEE,KAAKN;QAAOK;QAAQJ;IAAS;AACxC;AAEA,SAASM,qBAAqBC,KAAa;IACzC,MAAMC,WAAWC,IAAAA,wCAAmB,EAACF,OAAOJ,KAAK,CAAC,GAAGO,KAAK,CAAC;IAC3D,MAAMC,SAAyC,CAAC;IAChD,IAAIC,aAAa;IACjB,OAAO;QACLC,oBAAoBL,SACjBM,GAAG,CAAC,CAACC;YACJ,MAAMC,cAAcC,8CAA0B,CAACC,IAAI,CAAC,CAACC,IACnDJ,QAAQd,UAAU,CAACkB;YAErB,MAAMC,eAAeL,QAAQM,KAAK,CAAC,uBAAuB,uBAAuB;;YAEjF,IAAIL,eAAeI,cAAc;gBAC/B,MAAM,EAAEf,GAAG,EAAEL,QAAQ,EAAEI,MAAM,EAAE,GAAGN,eAAesB,YAAY,CAAC,EAAE;gBAChET,MAAM,CAACN,IAAI,GAAG;oBAAEiB,KAAKV;oBAAcR;oBAAQJ;gBAAS;gBACpD,OAAO,AAAC,MAAGuB,IAAAA,gCAAkB,EAACP,eAAa;YAC7C,OAAO,IAAII,cAAc;gBACvB,MAAM,EAAEf,GAAG,EAAED,MAAM,EAAEJ,QAAQ,EAAE,GAAGF,eAAesB,YAAY,CAAC,EAAE;gBAChET,MAAM,CAACN,IAAI,GAAG;oBAAEiB,KAAKV;oBAAcR;oBAAQJ;gBAAS;gBACpD,OAAOI,SAAUJ,WAAW,gBAAgB,WAAY;YAC1D,OAAO;gBACL,OAAO,AAAC,MAAGuB,IAAAA,gCAAkB,EAACR;YAChC;QACF,GACCS,IAAI,CAAC;QACRb;IACF;AACF;AAOO,SAASlB,cAAcgC,eAAuB;IACnD,MAAM,EAAEZ,kBAAkB,EAAEF,MAAM,EAAE,GAAGL,qBAAqBmB;IAC5D,OAAO;QACLC,IAAI,IAAIC,OAAO,AAAC,MAAGd,qBAAmB;QACtCF,QAAQA;IACV;AACF;AAEA;;;CAGC,GACD,SAASiB;IACP,IAAIC,IAAI;IAER,OAAO;QACL,IAAIC,WAAW;QACf,IAAIC,IAAI,EAAEF;QACV,MAAOE,IAAI,EAAG;YACZD,YAAYE,OAAOC,YAAY,CAAC,KAAM,AAACF,CAAAA,IAAI,CAAA,IAAK;YAChDA,IAAIG,KAAKC,KAAK,CAAC,AAACJ,CAAAA,IAAI,CAAA,IAAK;QAC3B;QACA,OAAOD;IACT;AACF;AAEA,SAASM,sBAAsB,KAU9B;IAV8B,IAAA,EAC7BC,eAAe,EACftB,OAAO,EACPuB,SAAS,EACTC,SAAS,EAMV,GAV8B;IAW7B,MAAM,EAAElC,GAAG,EAAEL,QAAQ,EAAEI,MAAM,EAAE,GAAGN,eAAeiB;IAEjD,uDAAuD;IACvD,kBAAkB;IAClB,IAAIyB,aAAanC,IAAIoC,OAAO,CAAC,OAAO;IAEpC,IAAIF,WAAW;QACbC,aAAa,AAAC,KAAED,YAAYC;IAC9B;IACA,IAAIE,aAAa;IAEjB,kEAAkE;IAClE,WAAW;IACX,IAAIF,WAAWG,MAAM,KAAK,KAAKH,WAAWG,MAAM,GAAG,IAAI;QACrDD,aAAa;IACf;IACA,IAAI,CAACE,MAAMC,SAASL,WAAWrC,KAAK,CAAC,GAAG,MAAM;QAC5CuC,aAAa;IACf;IAEA,IAAIA,YAAY;QACdF,aAAaH;IACf;IAEA,IAAIE,WAAW;QACbD,SAAS,CAACE,WAAW,GAAG,AAAC,KAAED,YAAYlC;IACzC,OAAO;QACLiC,SAAS,CAACE,WAAW,GAAG,AAAC,KAAEnC;IAC7B;IAEA,OAAOD,SACHJ,WACE,AAAC,YAASwC,aAAW,YACrB,AAAC,SAAMA,aAAW,UACpB,AAAC,SAAMA,aAAW;AACxB;AAEA,SAASM,0BAA0BvC,KAAa,EAAEwC,eAAwB;IACxE,MAAMvC,WAAWC,IAAAA,wCAAmB,EAACF,OAAOJ,KAAK,CAAC,GAAGO,KAAK,CAAC;IAC3D,MAAM2B,kBAAkBT;IACxB,MAAMU,YAAyC,CAAC;IAChD,OAAO;QACLU,yBAAyBxC,SACtBM,GAAG,CAAC,CAACC;YACJ,MAAMkC,wBAAwBhC,8CAA0B,CAACiC,IAAI,CAAC,CAAC/B,IAC7DJ,QAAQd,UAAU,CAACkB;YAErB,MAAMC,eAAeL,QAAQM,KAAK,CAAC,uBAAuB,uBAAuB;;YAEjF,IAAI4B,yBAAyB7B,cAAc;gBACzC,OAAOgB,sBAAsB;oBAC3BC;oBACAtB,SAASK,YAAY,CAAC,EAAE;oBACxBkB;oBACAC,WAAWQ,kBACPlD,kCACAsD;gBACN;YACF,OAAO,IAAI/B,cAAc;gBACvB,OAAOgB,sBAAsB;oBAC3BC;oBACAtB,SAASK,YAAY,CAAC,EAAE;oBACxBkB;oBACAC,WAAWQ,kBAAkBnD,0BAA0BuD;gBACzD;YACF,OAAO;gBACL,OAAO,AAAC,MAAG5B,IAAAA,gCAAkB,EAACR;YAChC;QACF,GACCS,IAAI,CAAC;QACRc;IACF;AACF;AAUO,SAAS5C,mBACd+B,eAAuB,EACvB2B,cAAuB;IAEvB,MAAMC,SAASP,0BAA0BrB,iBAAiB2B;IAC1D,OAAO;QACL,GAAG3D,cAAcgC,gBAAgB;QACjC6B,YAAY,AAAC,MAAGD,OAAOL,uBAAuB,GAAC;QAC/CV,WAAWe,OAAOf,SAAS;IAC7B;AACF;AAMO,SAAS3C,wBACd8B,eAAuB,EACvB8B,OAEC;IAED,MAAM,EAAE1C,kBAAkB,EAAE,GAAGP,qBAAqBmB;IACpD,MAAM,EAAE+B,WAAW,IAAI,EAAE,GAAGD;IAC5B,IAAI1C,uBAAuB,KAAK;QAC9B,IAAI4C,gBAAgBD,WAAW,OAAO;QACtC,OAAO;YACLF,YAAY,AAAC,OAAIG,gBAAc;QACjC;IACF;IAEA,MAAM,EAAET,uBAAuB,EAAE,GAAGF,0BAClCrB,iBACA;IAEF,IAAIiC,uBAAuBF,WAAW,eAAe;IACrD,OAAO;QACLF,YAAY,AAAC,MAAGN,0BAA0BU,uBAAqB;IACjE;AACF"}