{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-swc-loader.ts"], "names": ["pitch", "sw<PERSON><PERSON><PERSON><PERSON>", "raw", "loaderTransform", "parentTrace", "source", "inputSourceMap", "nextConfig", "filename", "resourcePath", "loaderOptions", "getOptions", "isServer", "rootDir", "pagesDir", "appDir", "hasReactRefresh", "jsConfig", "supportedBrowsers", "swcCacheDir", "serverComponents", "isReactServerLayer", "isPageFile", "startsWith", "relativeFilePathFromRoot", "path", "relative", "swcOptions", "getLoaderSWCOptions", "development", "mode", "modularizeImports", "optimizePackageImports", "experimental", "swcPlugins", "compilerOptions", "compiler", "optimizeServerReact", "programmaticOptions", "JSON", "stringify", "undefined", "sourceMaps", "sourceMap", "inlineSourcesContent", "sourceFileName", "jsc", "transform", "react", "Object", "prototype", "hasOwnProperty", "call", "swcSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "then", "output", "eliminatedPackages", "pkg", "parse", "add", "code", "map", "EXCLUDED_PATHS", "callback", "async", "process", "versions", "pnp", "test", "loaders", "length", "loaderIndex", "isAbsolute", "isWasm", "loaderSpan", "currentTraceSpan", "addDependency", "r", "inputSource", "transformedSource", "outputSourceMap", "err"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA;;;;;;;;;;;;;;;;IAwHgBA,KAAK;eAALA;;IAuBhB,OAmBC;eAnBuBC;;IAsBXC,GAAG;eAAHA;;;qBAlKqB;yBACE;8DACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBjC,eAAeC,gBAEbC,WAAgB,EAChBC,MAAe,EACfC,cAAoB;QAgCMC,0BACZA,2BAESA;IAjCvB,wBAAwB;IACxB,MAAMC,WAAW,IAAI,CAACC,YAAY;IAElC,IAAIC,gBAAkC,IAAI,CAACC,UAAU,MAAM,CAAC;IAE5D,MAAM,EACJC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,eAAe,EACfT,UAAU,EACVU,QAAQ,EACRC,iBAAiB,EACjBC,WAAW,EACXC,gBAAgB,EAChBC,kBAAkB,EACnB,GAAGX;IACJ,MAAMY,aAAad,SAASe,UAAU,CAACT;IACvC,MAAMU,2BAA2BC,aAAI,CAACC,QAAQ,CAACb,SAASL;IAExD,MAAMmB,aAAaC,IAAAA,4BAAmB,EAAC;QACrCd;QACAC;QACAP;QACAI;QACAU;QACAO,aAAa,IAAI,CAACC,IAAI,KAAK;QAC3Bd;QACAe,iBAAiB,EAAExB,8BAAAA,WAAYwB,iBAAiB;QAChDC,sBAAsB,EAAEzB,+BAAAA,2BAAAA,WAAY0B,YAAY,qBAAxB1B,yBAA0ByB,sBAAsB;QACxEE,UAAU,EAAE3B,+BAAAA,4BAAAA,WAAY0B,YAAY,qBAAxB1B,0BAA0B2B,UAAU;QAChDC,eAAe,EAAE5B,8BAAAA,WAAY6B,QAAQ;QACrCC,mBAAmB,EAAE9B,+BAAAA,4BAAAA,WAAY0B,YAAY,qBAAxB1B,0BAA0B8B,mBAAmB;QAClEpB;QACAC;QACAC;QACAK;QACAJ;QACAC;IACF;IAEA,MAAMiB,sBAAsB;QAC1B,GAAGX,UAAU;QACbnB;QACAF,gBAAgBA,iBAAiBiC,KAAKC,SAAS,CAAClC,kBAAkBmC;QAElE,sEAAsE;QACtEC,YAAY,IAAI,CAACC,SAAS;QAC1BC,sBAAsB,IAAI,CAACD,SAAS;QAEpC,qEAAqE;QACrE,qEAAqE;QACrE,WAAW;QACXE,gBAAgBrC;IAClB;IAEA,IAAI,CAAC8B,oBAAoBhC,cAAc,EAAE;QACvC,OAAOgC,oBAAoBhC,cAAc;IAC3C;IAEA,+BAA+B;IAC/B,IACE,IAAI,CAACwB,IAAI,IACTQ,oBAAoBQ,GAAG,IACvBR,oBAAoBQ,GAAG,CAACC,SAAS,IACjCT,oBAAoBQ,GAAG,CAACC,SAAS,CAACC,KAAK,IACvC,CAACC,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CACnCd,oBAAoBQ,GAAG,CAACC,SAAS,CAACC,KAAK,EACvC,gBAEF;QACAV,oBAAoBQ,GAAG,CAACC,SAAS,CAACC,KAAK,CAACnB,WAAW,GACjD,IAAI,CAACC,IAAI,KAAK;IAClB;IAEA,MAAMuB,UAAUjD,YAAYkD,UAAU,CAAC;IACvC,OAAOD,QAAQE,YAAY,CAAC,IAC1BR,IAAAA,cAAS,EAAC1C,QAAeiC,qBAAqBkB,IAAI,CAAC,CAACC;YAClD,IAAIA,OAAOC,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,EAAE;gBACxD,KAAK,MAAMC,OAAOpB,KAAKqB,KAAK,CAACH,OAAOC,kBAAkB,EAAG;oBACvD,IAAI,CAACA,kBAAkB,CAACG,GAAG,CAACF;gBAC9B;YACF;YACA,OAAO;gBAACF,OAAOK,IAAI;gBAAEL,OAAOM,GAAG,GAAGxB,KAAKqB,KAAK,CAACH,OAAOM,GAAG,IAAItB;aAAU;QACvE;AAEJ;AAEA,MAAMuB,iBACJ;AAEK,SAAShE;IACd,MAAMiE,WAAW,IAAI,CAACC,KAAK;IACzB,CAAA;QACA,IACE,kDAAkD;QAClD,CAACC,QAAQC,QAAQ,CAACC,GAAG,IACrB,CAACL,eAAeM,IAAI,CAAC,IAAI,CAAC7D,YAAY,KACtC,IAAI,CAAC8D,OAAO,CAACC,MAAM,GAAG,MAAM,IAAI,CAACC,WAAW,IAC5CC,IAAAA,gBAAU,EAAC,IAAI,CAACjE,YAAY,KAC5B,CAAE,MAAMkE,IAAAA,WAAM,KACd;YACA,MAAMC,aAAa,IAAI,CAACC,gBAAgB,CAACvB,UAAU,CAAC;YACpD,IAAI,CAACwB,aAAa,CAAC,IAAI,CAACrE,YAAY;YACpC,OAAOmE,WAAWrB,YAAY,CAAC,IAC7BpD,gBAAgBiD,IAAI,CAAC,IAAI,EAAEwB;QAE/B;IACF,CAAA,IAAKpB,IAAI,CAAC,CAACuB;QACT,IAAIA,GAAG,OAAOd,SAAS,SAASc;QAChCd;IACF,GAAGA;AACL;AAEe,SAAShE,UAEtB+E,WAAmB,EACnB1E,cAAmB;IAEnB,MAAMsE,aAAa,IAAI,CAACC,gBAAgB,CAACvB,UAAU,CAAC;IACpD,MAAMW,WAAW,IAAI,CAACC,KAAK;IAC3BU,WACGrB,YAAY,CAAC,IACZpD,gBAAgBiD,IAAI,CAAC,IAAI,EAAEwB,YAAYI,aAAa1E,iBAErDkD,IAAI,CACH,CAAC,CAACyB,mBAAmBC,gBAAqB;QACxCjB,SAAS,MAAMgB,mBAAmBC,mBAAmB5E;IACvD,GACA,CAAC6E;QACClB,SAASkB;IACX;AAEN;AAGO,MAAMjF,MAAM"}