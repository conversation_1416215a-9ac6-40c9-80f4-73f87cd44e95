{"version": 3, "sources": ["../../../src/server/dev/static-paths-worker.ts"], "names": ["buildAppStaticPaths", "buildStaticPaths", "collectGenerateParams", "loadComponents", "setHttpClientAndAgentOptions", "serverHooks", "staticGenerationAsyncStorage", "AppRouteRouteModule", "require", "loadStaticPaths", "distDir", "pathname", "config", "httpAgentOptions", "locales", "defaultLocale", "isAppPath", "page", "isrFlushToDisk", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "requestHeaders", "incremental<PERSON>ache<PERSON>andlerPath", "ppr", "setConfig", "components", "getStaticPaths", "Error", "routeModule", "generateParams", "is", "revalidate", "userland", "dynamic", "dynamicParams", "generateStaticParams", "segmentPath", "ComponentMod", "tree", "configFileName"], "mappings": "AAEA,OAAO,kBAAiB;AACxB,OAAO,sBAAqB;AAE5B,SACEA,mBAAmB,EACnBC,gBAAgB,EAChBC,qBAAqB,QAChB,oBAAmB;AAE1B,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,4BAA4B,QAAQ,0BAAyB;AAEtE,YAAYC,iBAAiB,+CAA8C;AAC3E,SAASC,4BAA4B,QAAQ,mEAAkE;AAE/G,MAAM,EAAEC,mBAAmB,EAAE,GAC3BC,QAAQ;AAQV,yDAAyD;AACzD,uDAAuD;AACvD,4BAA4B;AAC5B,OAAO,eAAeC,gBAAgB,EACpCC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,gBAAgB,EAChBC,OAAO,EACPC,aAAa,EACbC,SAAS,EACTC,IAAI,EACJC,cAAc,EACdC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,2BAA2B,EAC3BC,GAAG,EAgBJ;IAKC,oCAAoC;IACpCf,QAAQ,4CAA4CgB,SAAS,CAACZ;IAC9DR,6BAA6B;QAC3BS;IACF;IAEA,MAAMY,aAAa,MAAMtB,eAAe;QACtCO;QACA,qDAAqD;QACrDO,MAAMA,QAAQN;QACdK;IACF;IAEA,IAAI,CAACS,WAAWC,cAAc,IAAI,CAACV,WAAW;QAC5C,yDAAyD;QACzD,mDAAmD;QACnD,MAAM,IAAIW,MACR,CAAC,uDAAuD,EAAEhB,SAAS,CAAC;IAExE;IAEA,IAAIK,WAAW;QACb,MAAM,EAAEY,WAAW,EAAE,GAAGH;QACxB,MAAMI,iBACJD,eAAerB,oBAAoBuB,EAAE,CAACF,eAClC;YACE;gBACEhB,QAAQ;oBACNmB,YAAYH,YAAYI,QAAQ,CAACD,UAAU;oBAC3CE,SAASL,YAAYI,QAAQ,CAACC,OAAO;oBACrCC,eAAeN,YAAYI,QAAQ,CAACE,aAAa;gBACnD;gBACAC,sBAAsBP,YAAYI,QAAQ,CAACG,oBAAoB;gBAC/DC,aAAazB;YACf;SACD,GACD,MAAMT,sBAAsBuB,WAAWY,YAAY,CAACC,IAAI;QAE9D,OAAO,MAAMtC,oBAAoB;YAC/BiB,MAAMN;YACNkB;YACAU,gBAAgB3B,OAAO2B,cAAc;YACrC7B;YACAW;YACAC;YACAjB;YACAC;YACAY;YACAC;YACAC;YACAG;QACF;IACF;IAEA,OAAO,MAAMtB,iBAAiB;QAC5BgB,MAAMN;QACNe,gBAAgBD,WAAWC,cAAc;QACzCa,gBAAgB3B,OAAO2B,cAAc;QACrCzB;QACAC;IACF;AACF"}